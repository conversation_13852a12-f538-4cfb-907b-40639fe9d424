// 拍照巡更页面
const app = getApp()

Page({
  data: {
    recordId: '',
    taskInfo: null,
    loading: false,
    submitting: false,
    
    // 位置信息
    currentLocation: null,
    locationLoading: false,
    locationError: '',
    
    // 照片信息
    photoList: [],
    maxPhotos: 9,
    
    // 表单数据
    remark: '',
    
    // 距离验证
    distanceFromTarget: 0,
    isLocationValid: false
  },

  onLoad(options) {
    const recordId = options.recordId
    if (!recordId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ recordId })
    this.loadTaskInfo()
  },

  // 加载任务信息
  async loadTaskInfo() {
    this.setData({ loading: true })
    
    try {
      const res = await app.request({
        url: `/api/wx/patrol/getTaskDetail/${this.data.recordId}`,
        method: 'GET'
      })

      if (res.code === 0) {
        this.setData({ taskInfo: res.data })
        wx.setNavigationBarTitle({
          title: `巡更 - ${res.data.location_name}`
        })
        // 自动获取位置
        this.getCurrentLocation()
      } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('加载任务信息失败:', error)
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 获取当前位置
  getCurrentLocation() {
    this.setData({ locationLoading: true, locationError: '' })
    
    wx.getLocation({
      type: 'gcj02',
      altitude: true,
      success: (res) => {
        const currentLocation = {
          latitude: res.latitude,
          longitude: res.longitude,
          accuracy: res.accuracy,
          altitude: res.altitude
        }
        
        this.setData({ currentLocation })
        this.validateLocation(currentLocation)
      },
      fail: (error) => {
        console.error('获取位置失败:', error)
        let errorMsg = '获取位置失败'
        
        if (error.errMsg.includes('auth deny')) {
          errorMsg = '请授权位置权限'
        } else if (error.errMsg.includes('timeout')) {
          errorMsg = '定位超时，请重试'
        }
        
        this.setData({ locationError: errorMsg })
        wx.showToast({
          title: errorMsg,
          icon: 'none'
        })
      },
      complete: () => {
        this.setData({ locationLoading: false })
      }
    })
  },

  // 验证位置是否在允许范围内
  validateLocation(currentLocation) {
    const { taskInfo } = this.data
    
    if (!taskInfo || !taskInfo.target_lng || !taskInfo.target_lat) {
      this.setData({ isLocationValid: true })
      return
    }
    
    const distance = this.calculateDistance(
      currentLocation.latitude,
      currentLocation.longitude,
      taskInfo.target_lat,
      taskInfo.target_lng
    )
    
    const allowedRange = taskInfo.location_range || 100
    const isValid = distance <= allowedRange
    
    this.setData({
      distanceFromTarget: Math.round(distance),
      isLocationValid: isValid
    })
    
    if (!isValid) {
      wx.showModal({
        title: '位置提醒',
        content: `当前位置距离目标地点${Math.round(distance)}米，超出允许范围${allowedRange}米。请移动到指定位置后再进行巡更。`,
        showCancel: false
      })
    }
  },

  // 计算两点间距离
  calculateDistance(lat1, lng1, lat2, lng2) {
    const R = 6371000 // 地球半径（米）
    
    const lat1Rad = lat1 * Math.PI / 180
    const lat2Rad = lat2 * Math.PI / 180
    const deltaLatRad = (lat2 - lat1) * Math.PI / 180
    const deltaLngRad = (lng2 - lng1) * Math.PI / 180
    
    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    
    return R * c
  },

  // 拍照
  takePhoto() {
    if (this.data.photoList.length >= this.data.maxPhotos) {
      wx.showToast({
        title: `最多上传${this.data.maxPhotos}张照片`,
        icon: 'none'
      })
      return
    }

    wx.chooseMedia({
      count: this.data.maxPhotos - this.data.photoList.length,
      mediaType: ['image'],
      sourceType: ['camera', 'album'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        this.uploadPhotos(res.tempFiles)
      }
    })
  },

  // 上传照片
  async uploadPhotos(tempFiles) {
    wx.showLoading({ title: '上传中...' })
    
    try {
      const uploadPromises = tempFiles.map(file => this.uploadSinglePhoto(file.tempFilePath))
      const results = await Promise.all(uploadPromises)
      
      const newPhotos = results.filter(result => result.success).map(result => ({
        fileId: result.fileId,
        url: result.url,
        tempFilePath: result.tempFilePath
      }))
      
      this.setData({
        photoList: [...this.data.photoList, ...newPhotos]
      })
      
      wx.showToast({
        title: `成功上传${newPhotos.length}张照片`,
        icon: 'success'
      })
      
    } catch (error) {
      console.error('上传照片失败:', error)
      wx.showToast({
        title: '上传失败',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 上传单张照片
  uploadSinglePhoto(tempFilePath) {
    return new Promise((resolve, reject) => {
      const token = wx.getStorageSync('token')
      const headers = token ? { 'Authorization': `Bearer ${token}` } : {}

      wx.uploadFile({
        url: `${app.globalData.baseUrl}/api/wx/file/upload`,
        filePath: tempFilePath,
        name: 'file',
        formData: {
          source: 'patrol',
          bucketType: 'public'
        },
        header: headers,
        success: (uploadRes) => {
          try {
            const data = JSON.parse(uploadRes.data)
            if (data.code === 0) {
              resolve({
                success: true,
                fileId: data.fileId,
                url: data.url,
                tempFilePath
              })
            } else {
              reject(new Error(data.msg || '上传失败'))
            }
          } catch (parseError) {
            reject(new Error('响应解析失败'))
          }
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  },

  // 删除照片
  deletePhoto(e) {
    const index = e.currentTarget.dataset.index
    const photoList = [...this.data.photoList]
    photoList.splice(index, 1)
    this.setData({ photoList })
  },

  // 预览照片
  previewPhoto(e) {
    const index = e.currentTarget.dataset.index
    const urls = this.data.photoList.map(photo => photo.url)
    
    wx.previewImage({
      current: urls[index],
      urls: urls
    })
  },

  // 输入备注
  onRemarkInput(e) {
    this.setData({ remark: e.detail.value })
  },

  // 提交巡更记录
  async submitPatrol() {
    // 验证必填项
    if (this.data.photoList.length === 0) {
      wx.showToast({
        title: '请至少上传一张照片',
        icon: 'none'
      })
      return
    }

    if (!this.data.currentLocation) {
      wx.showToast({
        title: '请先获取位置信息',
        icon: 'none'
      })
      return
    }

    if (!this.data.isLocationValid) {
      wx.showModal({
        title: '位置确认',
        content: '当前位置超出允许范围，确定要提交吗？',
        success: (res) => {
          if (res.confirm) {
            this.doSubmitPatrol()
          }
        }
      })
      return
    }

    this.doSubmitPatrol()
  },

  // 执行提交
  async doSubmitPatrol() {
    this.setData({ submitting: true })
    
    try {
      const photoFileIds = this.data.photoList.map(photo => photo.fileId)
      
      const res = await app.request({
        url: '/api/wx/patrol/submit',
        method: 'POST',
        data: {
          recordId: this.data.recordId,
          photoFileIds: photoFileIds,
          longitude: this.data.currentLocation.longitude,
          latitude: this.data.currentLocation.latitude,
          accuracy: this.data.currentLocation.accuracy,
          remark: this.data.remark
        }
      })

      if (res.code === 0) {
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: res.msg || '提交失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('提交巡更记录失败:', error)
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      })
    } finally {
      this.setData({ submitting: false })
    }
  },

  // 重新获取位置
  retryLocation() {
    this.getCurrentLocation()
  }
})
