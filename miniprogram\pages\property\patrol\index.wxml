<view class="patrol-container">
  <!-- 统计信息 -->
  <view class="statistics-card">
    <view class="stat-item">
      <view class="stat-number">{{statistics.totalTasks}}</view>
      <view class="stat-label">总任务</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{statistics.completedTasks}}</view>
      <view class="stat-label">已完成</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{statistics.pendingTasks}}</view>
      <view class="stat-label">待巡更</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{statistics.completionRate}}%</view>
      <view class="stat-label">完成率</view>
    </view>
  </view>

  <!-- 筛选按钮 -->
  <view class="filter-tabs">
    <view class="filter-tab {{filterStatus === 'all' ? 'active' : ''}}"
          bindtap="onFilterChange" data-status="all">
      全部
    </view>
    <view class="filter-tab {{filterStatus === 'pending' ? 'active' : ''}}"
          bindtap="onFilterChange" data-status="pending">
      待巡更
    </view>
    <view class="filter-tab {{filterStatus === 'completed' ? 'active' : ''}}"
          bindtap="onFilterChange" data-status="completed">
      已完成
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <van-loading type="spinner" size="24px" />
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 任务列表 -->
  <view wx:else class="task-list">
    <view wx:for="{{filteredTaskList || taskList}}" wx:key="record_id" class="task-item">
      <view class="task-header">
        <view class="location-info">
          <view class="location-name">{{item.location_name}}</view>
          <view class="location-address" wx:if="{{item.location_address}}">{{item.location_address}}</view>
        </view>
        <view class="task-status {{getStatusClass(item.status)}}">
          {{getStatusText(item.status)}}
        </view>
      </view>

      <view class="task-content">
        <view class="task-info">
          <view class="info-item">
            <text class="info-label">计划时间：</text>
            <text class="info-value">{{item.planned_time}}</text>
          </view>
          <view class="info-item" wx:if="{{item.actual_time}}">
            <text class="info-label">完成时间：</text>
            <text class="info-value">{{formatTime(item.actual_time)}}</text>
          </view>
          <view class="info-item" wx:if="{{item.photo_count > 0}}">
            <text class="info-label">照片数量：</text>
            <text class="info-value">{{item.photo_count}}张</text>
          </view>
        </view>
      </view>

      <view class="task-actions">
        <view wx:if="{{item.status === 0}}" class="action-btn primary"
              bindtap="startPatrol" data-id="{{item.record_id}}">
          开始巡更
        </view>
        <view wx:if="{{item.status === 1}}" class="action-btn secondary"
              bindtap="viewPatrolDetail" data-id="{{item.record_id}}">
          查看详情
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{filteredTaskList ? filteredTaskList.length === 0 : taskList.length === 0}}" class="empty-state">
      <image class="empty-icon" src="/images/empty-patrol.png" mode="aspectFit" />
      <text class="empty-text">今日暂无巡更任务</text>
      <text class="empty-tip">请联系管理员配置巡更任务</text>
    </view>
  </view>
</view>
