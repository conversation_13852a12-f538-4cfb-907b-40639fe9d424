// 巡更任务页面
import {handlePropertyPageShow} from '../../../utils/pageUtils.js'

const app = getApp()

Page({
  data: {
    title: '巡更任务',
    taskList: [],
    loading: false,
    refreshing: false,
    statistics: {
      totalTasks: 0,
      completedTasks: 0,
      pendingTasks: 0,
      completionRate: 0
    },
    filterStatus: 'all' // all, pending, completed
  },

  onLoad() {
    console.log('巡更任务页面加载')
    wx.setNavigationBarTitle({
      title: this.data.title
    })
  },

  onShow() {
    handlePropertyPageShow(this, this.loadTodayTasks)
  },

  // 加载今日巡更任务
  async loadTodayTasks() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const res = await app.request({
        url: '/api/wx/patrol/getTodayTasks',
        method: 'POST'
      })

      if (res.code === 0) {
        const data = res.data
        this.setData({
          taskList: data.tasks || [],
          statistics: {
            totalTasks: data.totalTasks || 0,
            completedTasks: data.completedTasks || 0,
            pendingTasks: data.pendingTasks || 0,
            completionRate: data.completionRate || 0
          }
        })
        this.filterTasks()
      } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('加载巡更任务失败:', error)
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      })
    } finally {
      this.setData({
        loading: false,
        refreshing: false
      })
    }
  },

  // 筛选任务
  filterTasks() {
    const { taskList, filterStatus } = this.data
    let filteredList = taskList

    if (filterStatus === 'pending') {
      filteredList = taskList.filter(task => task.status === 0)
    } else if (filterStatus === 'completed') {
      filteredList = taskList.filter(task => task.status === 1)
    }

    this.setData({ filteredTaskList: filteredList })
  },

  // 切换筛选状态
  onFilterChange(e) {
    const status = e.currentTarget.dataset.status
    this.setData({ filterStatus: status })
    this.filterTasks()
  },

  // 开始巡更
  startPatrol(e) {
    const recordId = e.currentTarget.dataset.id
    const task = this.data.taskList.find(t => t.record_id === recordId)

    if (!task) {
      wx.showToast({
        title: '任务不存在',
        icon: 'none'
      })
      return
    }

    if (task.status === 1) {
      wx.showToast({
        title: '该任务已完成',
        icon: 'none'
      })
      return
    }

    // 跳转到拍照巡更页面
    wx.navigateTo({
      url: `/pages/property/patrol/camera?recordId=${recordId}`
    })
  },

  // 查看巡更详情
  viewPatrolDetail(e) {
    const recordId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/property/patrol/detail?recordId=${recordId}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadTodayTasks().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 格式化时间显示
  formatTime(timeStr) {
    if (!timeStr) return ''
    return timeStr.substring(0, 16) // 显示到分钟
  },

  // 获取任务状态文本
  getStatusText(status) {
    switch (status) {
      case 0: return '待巡更'
      case 1: return '已完成'
      case 2: return '已过期'
      default: return '未知'
    }
  },

  // 获取任务状态样式
  getStatusClass(status) {
    switch (status) {
      case 0: return 'status-pending'
      case 1: return 'status-completed'
      case 2: return 'status-overdue'
      default: return 'status-unknown'
    }
  }
})
