<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('巡更记录管理')" />
    <style>
        .patrol-photo {
            max-width: 60px;
            max-height: 40px;
            cursor: pointer;
            border-radius: 4px;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        .status-overdue {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li class="select-time">
                                <label>巡更日期：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endTime]"/>
                            </li>
                            <li>
                                地点名称：<input type="text" name="location_name" th:value="${defaultLocationName}" placeholder="请输入地点名称" onkeypress="if(event.keyCode==13) $.table.search()"/>
                            </li>
                            <li>
                                巡更人员：<input type="text" name="patrol_user_name" placeholder="请输入巡更人员姓名" onkeypress="if(event.keyCode==13) $.table.search()"/>
                            </li>
                            <li class="select-time">
                                <label>状态：</label>
                                <select name="status">
                                    <option value="">所有</option>
                                    <option value="0">待巡更</option>
                                    <option value="1">已完成</option>
                                    <option value="2">已过期</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="oc:patrol:export">
                    <i class="fa fa-download"></i> 导出记录
                </a>
                <a class="btn btn-info" onclick="showStatistics()">
                    <i class="fa fa-bar-chart"></i> 统计分析
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <!-- 照片预览模态框 -->
    <div class="modal fade" id="photoModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">巡更照片</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="photoContainer" class="row"></div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/patrol";
        var defaultLocationName = [[${defaultLocationName}]];

        $(function() {
            var options = {
                url: prefix + "/record/list",
                exportUrl: prefix + "/record/export",
                modalName: "巡更记录",
                sortName: "patrol_date",
                sortOrder: "desc",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'record_id',
                    title: '记录ID',
                    visible: false
                },
                {
                    field: 'patrol_date',
                    title: '巡更日期',
                    sortable: true
                },
                {
                    field: 'location_name',
                    title: '地点名称',
                    sortable: true
                },
                {
                    field: 'planned_time',
                    title: '计划时间',
                    sortable: true
                },
                {
                    field: 'actual_time',
                    title: '完成时间',
                    formatter: function(value, row, index) {
                        return value ? value.substring(0, 16) : '-';
                    }
                },
                {
                    field: 'patrol_user_name',
                    title: '巡更人员'
                },
                {
                    field: 'photo_count',
                    title: '照片数量',
                    formatter: function(value, row, index) {
                        if (value > 0) {
                            return '<a href="javascript:void(0)" onclick="viewPhotos(\'' + row.record_id + '\')" class="text-primary">' + value + '张</a>';
                        }
                        return '0张';
                    }
                },
                {
                    field: 'distance_from_target',
                    title: '位置偏差',
                    formatter: function(value, row, index) {
                        if (value == null || value == 0) return '-';
                        var range = row.location_range || 100;
                        if (value <= range) {
                            return '<span class="text-success">' + Math.round(value) + '米</span>';
                        } else {
                            return '<span class="text-danger">' + Math.round(value) + '米</span>';
                        }
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        switch(value) {
                            case 0:
                                return '<span class="status-badge status-pending">待巡更</span>';
                            case 1:
                                return '<span class="status-badge status-completed">已完成</span>';
                            case 2:
                                return '<span class="status-badge status-overdue">已过期</span>';
                            default:
                                return '<span class="status-badge">未知</span>';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewDetail(\'' + row.record_id + '\')"><i class="fa fa-eye"></i>详情</a>');
                        if (row.latitude && row.longitude) {
                            actions.push(' <a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="viewLocation(' + row.latitude + ',' + row.longitude + ',\'' + row.location_name + '\')"><i class="fa fa-map-marker"></i>位置</a>');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);

            // 如果有默认地点名称，自动执行搜索
            if (defaultLocationName) {
                setTimeout(function() {
                    $.table.search();
                }, 100);
            }
        });

        // 查看照片
        function viewPhotos(recordId) {
            $.get(prefix + "/record/detail/" + recordId, function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    var photoContainer = $('#photoContainer');
                    photoContainer.empty();
                    
                    if (data.photoFileIds) {
                        try {
                            var fileIds = JSON.parse(data.photoFileIds);
                            if (fileIds && fileIds.length > 0) {
                                fileIds.forEach(function(fileId, index) {
                                    var photoUrl = ctx + "common/download?fileName=" + fileId;
                                    var photoHtml = '<div class="col-md-4 col-sm-6 mb-3">' +
                                        '<img src="' + photoUrl + '" class="img-responsive" style="width:100%;cursor:pointer;" onclick="previewImage(\'' + photoUrl + '\')">' +
                                        '</div>';
                                    photoContainer.append(photoHtml);
                                });
                            }
                        } catch (e) {
                            photoContainer.html('<p class="text-center">照片解析失败</p>');
                        }
                    } else {
                        photoContainer.html('<p class="text-center">暂无照片</p>');
                    }
                    
                    $('#photoModal').modal('show');
                } else {
                    $.modal.msgError(result.msg);
                }
            });
        }

        // 预览单张图片
        function previewImage(url) {
            window.open(url, '_blank');
        }

        // 查看详情
        function viewDetail(recordId) {
            var url = prefix + "/record/detail/" + recordId;
            $.modal.openTab("巡更记录详情", url);
        }

        // 查看位置
        function viewLocation(lat, lng, name) {
            var mapUrl = "https://uri.amap.com/marker?position=" + lng + "," + lat + "&name=" + encodeURIComponent(name);
            window.open(mapUrl, '_blank');
        }

        // 显示统计信息
        function showStatistics() {
            $.modal.openTab("巡更统计", prefix + "/statistics");
        }
    </script>
</body>
</html>
